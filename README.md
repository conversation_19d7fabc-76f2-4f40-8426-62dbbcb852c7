# 股票提示应用

这是一个Android应用，每15分钟检查股票提示API，并在有新信息时发送通知提醒，支持声音、震动和图标角标。

## 功能特性

1. **定时检查**：每15分钟请求API (`http://stock.tingtran.top/stocktip`)
2. **智能通知**：
   - 只有新内容或未查看内容才会发送通知
   - 用户打开应用后自动清除通知，避免重复提醒
   - 支持声音、震动、LED灯效果
   - 显示应用图标角标
3. **后台保活**：
   - 使用前台服务提高应用存活率
   - 系统重启后自动恢复任务
   - 请求电池优化白名单

## 技术实现

- **WorkManager**：实现可靠的后台定时任务
- **前台服务**：提高应用在后台的存活率
- **OkHttp**：进行网络请求
- **NotificationManager**：实现通知和声音提醒
- **SharedPreferences**：保存股票提示信息和查看状态

## 构建和运行

1. 使用Android Studio打开项目
2. 构建并运行应用
3. 首次运行时会请求必要权限

## 用户使用指南

### 首次安装后的设置

1. **授予通知权限**：确保能收到股票提示通知
2. **电池优化白名单**：应用会自动引导您将其加入白名单，确保后台正常运行
3. **允许后台运行**：在通知栏会看到"正在后台监控股票信息"的持续通知

### 日常使用

- **收到通知**：有新股票信息时会收到通知，包含声音和震动
- **查看信息**：点击通知或打开应用查看详细信息
- **避免重复提醒**：查看后不会重复发送相同内容的通知

### 厂商特殊设置

不同手机厂商可能需要额外设置：

#### 小米/红米
1. 设置 → 应用管理 → 股票提示 → 省电策略 → 无限制
2. 设置 → 应用管理 → 股票提示 → 自启动管理 → 允许

#### 华为/荣耀
1. 设置 → 应用和服务 → 应用管理 → 股票提示 → 电池 → 应用启动管理 → 手动管理
2. 允许自动启动、关联启动、后台活动

#### OPPO/OnePlus
1. 设置 → 电池 → 应用耗电管理 → 股票提示 → 允许后台运行
2. 设置 → 应用管理 → 股票提示 → 权限 → 自启动 → 允许

#### vivo
1. 设置 → 电池 → 后台应用管理 → 股票提示 → 允许后台高耗电
2. 设置 → 更多设置 → 应用管理 → 股票提示 → 权限管理 → 自启动 → 允许

## 注意事项

- **网络权限**：应用需要网络权限才能正常工作
- **角标功能**：在不同设备上的支持可能不同
- **电池续航**：15分钟的检查频率可能会影响电池续航
- **系统限制**：Android系统可能会根据设备状态调整实际执行频率