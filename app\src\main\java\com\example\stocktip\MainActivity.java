package com.example.stocktip;

import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.content.Context;
import android.content.SharedPreferences;
import android.os.Build;
import android.os.Bundle;
import android.widget.TextView;
import android.Manifest;
import android.content.pm.PackageManager;

import androidx.appcompat.app.AppCompatActivity;
import androidx.work.ExistingPeriodicWorkPolicy;
import androidx.work.PeriodicWorkRequest;
import androidx.work.WorkManager;
import androidx.annotation.NonNull;

import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import java.util.concurrent.TimeUnit;

import android.widget.Toast;

public class MainActivity extends AppCompatActivity {

    private TextView tvStockTip;
    public static final String CHANNEL_ID = "stock_tip_channel";
    public static final String PREFS_NAME = "StockTipPrefs";
    public static final String STOCK_TIP_KEY = "stockTip";

    private static final int NOTIFICATION_PERMISSION_REQUEST_CODE = 101;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        tvStockTip = findViewById(R.id.tvStockTip);

        // 请求通知权限
        requestNotificationPermission();

        // 创建通知渠道
        createNotificationChannel();

        // 从SharedPreferences加载保存的股票提示
        loadStockTip();

        // 设置定时任务
        scheduleStockTipWorker();
    }

    @Override
    protected void onResume() {
        super.onResume();
        // 每次恢复活动时重新加载股票提示
        loadStockTip();
    }

    private void requestNotificationPermission() {
        // 只在 Android 13 (API 33) 及以上版本需要请求权限
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // 检查权限是否已经被授予
            if (ContextCompat.checkSelfPermission(this,
                    Manifest.permission.POST_NOTIFICATIONS) == PackageManager.PERMISSION_GRANTED) {
                // 权限已经被授予，可以继续
            } else {
                // 权限未被授予，发起请求
                ActivityCompat.requestPermissions(this,
                        new String[] { Manifest.permission.POST_NOTIFICATIONS },
                        NOTIFICATION_PERMISSION_REQUEST_CODE);
            }
        }
    }

    // 处理权限请求的结果
    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions,
            @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == NOTIFICATION_PERMISSION_REQUEST_CODE) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                // 用户授予了权限
                Toast.makeText(this, "通知权限已授予！", Toast.LENGTH_SHORT).show();
            } else {
                // 用户拒绝了权限
                Toast.makeText(this, "您拒绝了通知权限，可能无法及时收到股票提示。", Toast.LENGTH_LONG).show();
            }
        }
    }

    private void loadStockTip() {
        SharedPreferences prefs = getSharedPreferences(PREFS_NAME, MODE_PRIVATE);
        String stockTip = prefs.getString(STOCK_TIP_KEY, "");
        if (!stockTip.isEmpty()) {
            tvStockTip.setText(stockTip);
        } else {
            tvStockTip.setText(R.string.no_stock_tip);
        }
    }

    private void createNotificationChannel() {
        // 创建通知渠道，但仅在API 26+以上版本需要，因为通知渠道是Android 8.0引入的
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            CharSequence name = "股票提示";
            String description = "每日股票提示通知";
            // 将重要性级别改为HIGH
            int importance = NotificationManager.IMPORTANCE_HIGH;
            NotificationChannel channel = new NotificationChannel(CHANNEL_ID, name, importance);
            channel.setDescription(description);
            // 确保启用声音和震动
            channel.enableVibration(true);
            channel.enableLights(true);
            // 设置通知圆点
            channel.setShowBadge(true);
            // 注册通知渠道
            NotificationManager notificationManager = getSystemService(NotificationManager.class);
            notificationManager.createNotificationChannel(channel);
        }
    }

    private void scheduleStockTipWorker() {
        // 创建周期性工作请求，每天执行一次
        PeriodicWorkRequest stockTipWorkRequest = new PeriodicWorkRequest.Builder(StockTipWorker.class, 15,
                TimeUnit.MINUTES)
                .build();

        // 将工作请求加入队列
        WorkManager.getInstance(this).enqueueUniquePeriodicWork(
                "stockTipWork",
                ExistingPeriodicWorkPolicy.REPLACE,
                stockTipWorkRequest);
    }

    // 更新UI上的股票提示
    public static void updateStockTip(Context context, String tip) {
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, MODE_PRIVATE);
        SharedPreferences.Editor editor = prefs.edit();
        editor.putString(STOCK_TIP_KEY, tip);
        editor.apply();
    }
}